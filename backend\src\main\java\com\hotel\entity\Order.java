package com.hotel.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单实体类
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("orders")
public class Order implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 桌号
     */
    @TableField("table_number")
    private String tableNumber;

    /**
     * 用户ID（服务员）
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 状态：PENDING-待处理，PREPARING-准备中，COMPLETED-已完成，CANCELLED-已取消
     */
    @TableField("status")
    private String status;

    /**
     * 总价格
     */
    @TableField("total_price")
    private BigDecimal totalPrice;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标志
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 服务员姓名（非数据库字段）
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 订单明细列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<OrderItem> items;
}
