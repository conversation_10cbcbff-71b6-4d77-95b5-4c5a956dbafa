package com.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hotel.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM user WHERE username = #{username} AND deleted = 0")
    User findByUsername(@Param("username") String username);

    /**
     * 分页查询用户列表
     */
    @Select("<script>" +
            "SELECT * FROM user WHERE deleted = 0" +
            "<if test='username != null and username != \"\"'>" +
            " AND username LIKE CONCAT('%', #{username}, '%')" +
            "</if>" +
            "<if test='name != null and name != \"\"'>" +
            " AND name LIKE CONCAT('%', #{name}, '%')" +
            "</if>" +
            "<if test='role != null and role != \"\"'>" +
            " AND role = #{role}" +
            "</if>" +
            " ORDER BY created_at DESC" +
            "</script>")
    IPage<User> selectUserPage(Page<User> page, 
                               @Param("username") String username,
                               @Param("name") String name, 
                               @Param("role") String role);

    /**
     * 更新最后登录时间
     */
    @Update("UPDATE user SET last_login_at = NOW() WHERE id = #{userId}")
    int updateLastLoginTime(@Param("userId") Integer userId);

    /**
     * 统计管理员数量
     */
    @Select("SELECT COUNT(*) FROM user WHERE role = 'ADMIN' AND deleted = 0")
    int countAdmins();

    /**
     * 检查用户名是否存在
     */
    @Select("SELECT COUNT(*) FROM user WHERE username = #{username} AND deleted = 0")
    int checkUsernameExists(@Param("username") String username);
}
