package com.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hotel.entity.Notification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 通知Mapper接口
 */
@Mapper
public interface NotificationMapper extends BaseMapper<Notification> {
    
    /**
     * 获取用户未读通知数量
     */
    Integer getUnreadCount(@Param("userId") Integer userId);
    
    /**
     * 标记用户所有通知为已读
     */
    void markAllAsRead(@Param("userId") Integer userId);
}
