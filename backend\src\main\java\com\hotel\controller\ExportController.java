package com.hotel.controller;

import com.hotel.service.ExportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 导出控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/export")
public class ExportController {
    
    @Autowired
    private ExportService exportService;
    
    /**
     * 导出订单报表
     */
    @GetMapping("/orders")
    public ResponseEntity<byte[]> exportOrders(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        try {
            log.info("导出订单报表，开始日期: {}, 结束日期: {}", startDate, endDate);
            
            ByteArrayOutputStream outputStream = exportService.exportOrders(startDate, endDate);
            byte[] bytes = outputStream.toByteArray();
            
            String filename = generateFilename("订单报表");
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, 
                           "attachment; filename*=UTF-8''" + URLEncoder.encode(filename, StandardCharsets.UTF_8))
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(bytes);
                    
        } catch (Exception e) {
            log.error("导出订单报表失败", e);
            throw new RuntimeException("导出失败");
        }
    }
    
    /**
     * 导出用户报表
     */
    @GetMapping("/users")
    public ResponseEntity<byte[]> exportUsers() {
        try {
            log.info("导出用户报表");
            
            ByteArrayOutputStream outputStream = exportService.exportUsers();
            byte[] bytes = outputStream.toByteArray();
            
            String filename = generateFilename("用户报表");
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, 
                           "attachment; filename*=UTF-8''" + URLEncoder.encode(filename, StandardCharsets.UTF_8))
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(bytes);
                    
        } catch (Exception e) {
            log.error("导出用户报表失败", e);
            throw new RuntimeException("导出失败");
        }
    }
    
    /**
     * 导出菜品报表
     */
    @GetMapping("/dishes")
    public ResponseEntity<byte[]> exportDishes() {
        try {
            log.info("导出菜品报表");
            
            ByteArrayOutputStream outputStream = exportService.exportDishes();
            byte[] bytes = outputStream.toByteArray();
            
            String filename = generateFilename("菜品报表");
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, 
                           "attachment; filename*=UTF-8''" + URLEncoder.encode(filename, StandardCharsets.UTF_8))
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(bytes);
                    
        } catch (Exception e) {
            log.error("导出菜品报表失败", e);
            throw new RuntimeException("导出失败");
        }
    }
    
    /**
     * 导出销售报表
     */
    @GetMapping("/sales")
    public ResponseEntity<byte[]> exportSales(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        try {
            log.info("导出销售报表，开始日期: {}, 结束日期: {}", startDate, endDate);
            
            ByteArrayOutputStream outputStream = exportService.exportSales(startDate, endDate);
            byte[] bytes = outputStream.toByteArray();
            
            String filename = generateFilename("销售报表");
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, 
                           "attachment; filename*=UTF-8''" + URLEncoder.encode(filename, StandardCharsets.UTF_8))
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(bytes);
                    
        } catch (Exception e) {
            log.error("导出销售报表失败", e);
            throw new RuntimeException("导出失败");
        }
    }
    
    /**
     * 生成文件名
     */
    private String generateFilename(String prefix) {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return prefix + "_" + dateStr + ".xlsx";
    }
}
