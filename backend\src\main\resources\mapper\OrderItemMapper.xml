<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hotel.mapper.OrderItemMapper">

    <!-- 批量插入订单明细 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO order_item (order_id, dish_id, quantity, price, notes)
        VALUES
        <foreach collection="items" item="item" separator=",">
            (#{item.orderId}, #{item.dishId}, #{item.quantity}, #{item.price}, #{item.notes})
        </foreach>
    </insert>

    <!-- 根据订单ID删除订单明细 -->
    <delete id="deleteByOrderId" parameterType="java.lang.Integer">
        DELETE FROM order_item WHERE order_id = #{orderId}
    </delete>

</mapper>
