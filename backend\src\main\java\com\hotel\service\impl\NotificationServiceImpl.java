package com.hotel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hotel.entity.Notification;
import com.hotel.mapper.NotificationMapper;
import com.hotel.service.NotificationService;
import com.hotel.common.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 通知服务实现类
 */
@Slf4j
@Service
public class NotificationServiceImpl extends ServiceImpl<NotificationMapper, Notification> implements NotificationService {
    
    @Override
    public PageResult<Notification> getUserNotifications(Integer userId, Integer page, Integer size) {
        Page<Notification> pageObj = new Page<>(page, size);
        
        LambdaQueryWrapper<Notification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper -> 
            wrapper.eq(Notification::getUserId, userId)
                   .or()
                   .isNull(Notification::getUserId)
        );
        queryWrapper.orderByDesc(Notification::getCreatedAt);
        
        Page<Notification> result = this.page(pageObj, queryWrapper);
        
        return new PageResult<>(
            result.getRecords(),
            result.getTotal(),
            result.getCurrent(),
            result.getSize()
        );
    }
    
    @Override
    public void markAsRead(Integer id, Integer userId) {
        LambdaUpdateWrapper<Notification> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Notification::getId, id)
                    .and(wrapper -> 
                        wrapper.eq(Notification::getUserId, userId)
                               .or()
                               .isNull(Notification::getUserId)
                    )
                    .set(Notification::getIsRead, 1);
        
        this.update(updateWrapper);
        log.info("用户 {} 标记通知 {} 为已读", userId, id);
    }
    
    @Override
    public void markAllAsRead(Integer userId) {
        LambdaUpdateWrapper<Notification> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.and(wrapper -> 
                        wrapper.eq(Notification::getUserId, userId)
                               .or()
                               .isNull(Notification::getUserId)
                    )
                    .set(Notification::getIsRead, 1);
        
        this.update(updateWrapper);
        log.info("用户 {} 标记所有通知为已读", userId);
    }
    
    @Override
    public Integer getUnreadCount(Integer userId) {
        LambdaQueryWrapper<Notification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper -> 
                        wrapper.eq(Notification::getUserId, userId)
                               .or()
                               .isNull(Notification::getUserId)
                    )
                    .eq(Notification::getIsRead, 0);
        
        return Math.toIntExact(this.count(queryWrapper));
    }
    
    @Override
    public void createSystemNotification(String title, String message, String type) {
        Notification notification = new Notification();
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setType(type);
        notification.setUserId(null); // 系统通知，所有用户可见
        notification.setIsRead(0);
        
        this.save(notification);
        log.info("创建系统通知: {}", title);
    }
    
    @Override
    public void createUserNotification(Integer userId, String title, String message, String type, String link) {
        Notification notification = new Notification();
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setType(type);
        notification.setUserId(userId);
        notification.setIsRead(0);
        notification.setLink(link);
        
        this.save(notification);
        log.info("为用户 {} 创建通知: {}", userId, title);
    }
    
    @Override
    public void createOrderNotification(Integer orderId, String title, String message) {
        Notification notification = new Notification();
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setType("order");
        notification.setUserId(null); // 订单通知，所有用户可见
        notification.setIsRead(0);
        notification.setLink("/orders/" + orderId);
        
        this.save(notification);
        log.info("创建订单通知: {} (订单ID: {})", title, orderId);
    }

    @Override
    public void removeById(Integer id) {
        super.removeById(id);
        log.info("删除通知: {}", id);
    }
}
