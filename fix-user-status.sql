-- 修复用户状态脚本
USE hotel_order_system;

-- 查看当前用户状态
SELECT id, username, name, role, status, created_at 
FROM user 
WHERE deleted = 0;

-- 启用所有被禁用的用户
UPDATE user 
SET status = 1 
WHERE status = 0 AND deleted = 0;

-- 确保有可用的测试用户
INSERT IGNORE INTO user (username, password, role, name, status) VALUES
('test01', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLO.TAGxK6FO', 'ADMIN', '测试管理员', 1),
('test02', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLO.TAGxK6FO', 'STAFF', '测试服务员', 1);

-- 再次查看用户状态
SELECT id, username, name, role, status, created_at 
FROM user 
WHERE deleted = 0;

-- 显示修复结果
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '用户状态修复完成！'
        ELSE '没有找到用户数据'
    END as '修复状态',
    COUNT(*) as '启用用户数量'
FROM user 
WHERE status = 1 AND deleted = 0;
