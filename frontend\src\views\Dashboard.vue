<template>
  <div class="dashboard">
    <h1 style="color: #409eff; margin-bottom: 20px;">🏨 酒店点单系统 - 控制面板</h1>

    <!-- 调试信息 -->
    <el-alert
      title="✅ 页面加载成功！"
      type="success"
      :closable="false"
      style="margin-bottom: 20px;"
    >
      <p>如果您能看到这条消息，说明Dashboard页面已经正常加载</p>
      <p>当前时间: {{ currentTime }}</p>
    </el-alert>

    <!-- 简化的统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #409eff; margin: 0;">42</h2>
          <p style="margin: 5px 0 0 0; color: #666;">今日订单</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #e6a23c; margin: 0;">8</h2>
          <p style="margin: 5px 0 0 0; color: #666;">待处理</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #67c23a; margin: 0;">¥2850</h2>
          <p style="margin: 5px 0 0 0; color: #666;">今日销售</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #f56c6c; margin: 0;">38</h2>
          <p style="margin: 5px 0 0 0; color: #666;">菜品总数</p>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card>
      <template #header>
        <span>🚀 快速操作</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-button type="primary" style="width: 100%;" @click="$router.push('/orders/create')">
            📝 创建订单
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" style="width: 100%;" @click="$router.push('/dishes')">
            🍽️ 管理菜品
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" style="width: 100%;" @click="$router.push('/orders')">
            📋 查看订单
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" style="width: 100%;" @click="$router.push('/reports')">
            📊 查看报表
          </el-button>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending-orders">
              <el-icon size="24"><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.pendingOrders }}</div>
              <div class="stat-label">待处理订单</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon today-sales">
              <el-icon size="24"><Money /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">¥{{ stats.todaySales }}</div>
              <div class="stat-label">今日销售额</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total-dishes">
              <el-icon size="24"><Food /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalDishes }}</div>
              <div class="stat-label">菜品总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    


<script setup>
import { ref, onMounted } from 'vue'

const currentTime = ref('')

const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

onMounted(() => {
  console.log('Dashboard mounted successfully!')
  updateTime()
  setInterval(updateTime, 1000)
})

// 简化版本，移除了复杂的数据和函数

onMounted(() => {
  // 加载数据
  console.log('Dashboard mounted')
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.today-orders {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.pending-orders {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.today-sales {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.total-dishes {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.content-row {
  margin-bottom: 20px;
}

.chart-card,
.recent-orders {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  padding: 10px 0;
}

@media (max-width: 768px) {
  .content-row .el-col {
    margin-bottom: 20px;
  }
}
</style>
