<template>
  <div class="dashboard">
    <h1 style="color: #409eff; margin-bottom: 20px;">🏨 酒店点单系统 - 控制面板</h1>

    <!-- 调试信息 -->
    <el-alert
      title="✅ 页面加载成功！"
      type="success"
      :closable="false"
      style="margin-bottom: 20px;"
    >
      <p>如果您能看到这条消息，说明Dashboard页面已经正常加载</p>
      <p>当前时间: {{ currentTime }}</p>
    </el-alert>

    <!-- 简化的统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #409eff; margin: 0;">42</h2>
          <p style="margin: 5px 0 0 0; color: #666;">今日订单</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #e6a23c; margin: 0;">8</h2>
          <p style="margin: 5px 0 0 0; color: #666;">待处理</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #67c23a; margin: 0;">¥2850</h2>
          <p style="margin: 5px 0 0 0; color: #666;">今日销售</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #f56c6c; margin: 0;">38</h2>
          <p style="margin: 5px 0 0 0; color: #666;">菜品总数</p>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card>
      <template #header>
        <span>🚀 快速操作</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-button type="primary" style="width: 100%;" @click="$router.push('/orders/create')">
            📝 创建订单
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" style="width: 100%;" @click="$router.push('/dishes')">
            🍽️ 管理菜品
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" style="width: 100%;" @click="$router.push('/orders')">
            📋 查看订单
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" style="width: 100%;" @click="$router.push('/reports')">
            📊 查看报表
          </el-button>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending-orders">
              <el-icon size="24"><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.pendingOrders }}</div>
              <div class="stat-label">待处理订单</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon today-sales">
              <el-icon size="24"><Money /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">¥{{ stats.todaySales }}</div>
              <div class="stat-label">今日销售额</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total-dishes">
              <el-icon size="24"><Food /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalDishes }}</div>
              <div class="stat-label">菜品总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 图表和表格 -->
    <el-row :gutter="20" class="content-row">
      <!-- 销售趋势图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>销售趋势</span>
              <el-button type="text" @click="refreshChart">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </template>
          <div class="chart-container">
            <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #909399;">
              销售趋势图表（开发中）
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 菜品分类统计 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>菜品分类统计</span>
            </div>
          </template>
          <div class="chart-container">
            <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #909399;">
              菜品分类统计图表（开发中）
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近订单 -->
    <el-card class="recent-orders">
      <template #header>
        <div class="card-header">
          <span>最近订单</span>
          <el-button type="primary" @click="$router.push('/orders')">
            查看全部
          </el-button>
        </div>
      </template>
      
      <el-table :data="recentOrders" style="width: 100%">
        <el-table-column prop="id" label="订单号" width="120">
          <template #default="{ row }">
            #{{ row.id }}
          </template>
        </el-table-column>
        <el-table-column prop="tableNumber" label="桌号" width="100" />
        <el-table-column prop="userName" label="服务员" width="120" />
        <el-table-column prop="totalPrice" label="金额" width="100">
          <template #default="{ row }">
            ¥{{ row.totalPrice }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="text" @click="viewOrder(row.id)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const currentTime = ref('')

const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

onMounted(() => {
  console.log('Dashboard mounted successfully!')
  updateTime()
  setInterval(updateTime, 1000)
})

const recentOrders = ref([
  {
    id: 'ORD-2023078',
    tableNumber: 'VIP3',
    userName: '服务员A',
    totalPrice: 106.00,
    status: 'COMPLETED',
    createdAt: '2023-07-15 14:30:00'
  },
  {
    id: 'ORD-2023077',
    tableNumber: 'A12',
    userName: '服务员B',
    totalPrice: 76.00,
    status: 'PREPARING',
    createdAt: '2023-07-15 13:45:00'
  },
  {
    id: 'ORD-2023076',
    tableNumber: 'B05',
    userName: '服务员A',
    totalPrice: 142.00,
    status: 'PENDING',
    createdAt: '2023-07-15 12:20:00'
  }
])

// 图表配置已简化，使用占位符显示

const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'PREPARING': 'info',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待处理',
    'PREPARING': '准备中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const viewOrder = (orderId) => {
  router.push(`/orders/${orderId}`)
}

const refreshChart = () => {
  // 刷新图表数据
  console.log('刷新图表')
}

onMounted(() => {
  // 加载数据
  console.log('Dashboard mounted')
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.today-orders {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.pending-orders {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.today-sales {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.total-dishes {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.content-row {
  margin-bottom: 20px;
}

.chart-card,
.recent-orders {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  padding: 10px 0;
}

@media (max-width: 768px) {
  .content-row .el-col {
    margin-bottom: 20px;
  }
}
</style>
