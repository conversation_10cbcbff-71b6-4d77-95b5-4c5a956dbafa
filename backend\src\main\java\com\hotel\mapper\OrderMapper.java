package com.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hotel.entity.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 订单Mapper接口
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Mapper
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 分页查询订单列表（带用户名）
     */
    @Select("<script>" +
            "SELECT o.*, u.name as user_name FROM orders o " +
            "LEFT JOIN user u ON o.user_id = u.id " +
            "WHERE o.deleted = 0" +
            "<if test='orderId != null and orderId != \"\"'>" +
            " AND o.id LIKE CONCAT('%', #{orderId}, '%')" +
            "</if>" +
            "<if test='tableNumber != null and tableNumber != \"\"'>" +
            " AND o.table_number LIKE CONCAT('%', #{tableNumber}, '%')" +
            "</if>" +
            "<if test='status != null and status != \"\"'>" +
            " AND o.status = #{status}" +
            "</if>" +
            "<if test='startDate != null'>" +
            " AND o.created_at >= #{startDate}" +
            "</if>" +
            "<if test='endDate != null'>" +
            " AND o.created_at &lt;= #{endDate}" +
            "</if>" +
            " ORDER BY o.created_at DESC" +
            "</script>")
    IPage<Order> selectOrderPage(Page<Order> page,
                                 @Param("orderId") String orderId,
                                 @Param("tableNumber") String tableNumber,
                                 @Param("status") String status,
                                 @Param("startDate") LocalDateTime startDate,
                                 @Param("endDate") LocalDateTime endDate);

    /**
     * 根据ID查询订单详情（带用户名）
     */
    @Select("SELECT o.*, u.name as user_name FROM orders o " +
            "LEFT JOIN user u ON o.user_id = u.id " +
            "WHERE o.id = #{id} AND o.deleted = 0")
    Order selectOrderWithUser(@Param("id") Integer id);

    /**
     * 获取今日订单统计
     */
    @Select("SELECT " +
            "COUNT(*) as total_orders, " +
            "COALESCE(SUM(total_price), 0) as total_revenue, " +
            "COALESCE(AVG(total_price), 0) as avg_order_value " +
            "FROM orders " +
            "WHERE DATE(created_at) = CURDATE() AND deleted = 0")
    Map<String, Object> selectTodayStats();

    /**
     * 获取订单状态统计
     */
    @Select("SELECT status, COUNT(*) as count FROM orders " +
            "WHERE deleted = 0 " +
            "<if test='startDate != null'>" +
            " AND created_at >= #{startDate}" +
            "</if>" +
            "<if test='endDate != null'>" +
            " AND created_at &lt;= #{endDate}" +
            "</if>" +
            " GROUP BY status")
    List<Map<String, Object>> selectStatusStats(@Param("startDate") LocalDateTime startDate,
                                                 @Param("endDate") LocalDateTime endDate);

    /**
     * 获取销售趋势数据
     */
    @Select("SELECT " +
            "DATE(created_at) as date, " +
            "COUNT(*) as order_count, " +
            "COALESCE(SUM(total_price), 0) as revenue " +
            "FROM orders " +
            "WHERE created_at >= #{startDate} AND created_at <= #{endDate} AND deleted = 0 " +
            "GROUP BY DATE(created_at) " +
            "ORDER BY date")
    List<Map<String, Object>> selectSalesTrend(@Param("startDate") LocalDateTime startDate,
                                               @Param("endDate") LocalDateTime endDate);

    /**
     * 获取时段分析数据
     */
    @Select("SELECT " +
            "HOUR(created_at) as hour, " +
            "COUNT(*) as order_count " +
            "FROM orders " +
            "WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND deleted = 0 " +
            "GROUP BY HOUR(created_at) " +
            "ORDER BY hour")
    List<Map<String, Object>> selectHourlyAnalysis();

    /**
     * 获取最近订单
     */
    @Select("SELECT o.*, u.name as user_name FROM orders o " +
            "LEFT JOIN user u ON o.user_id = u.id " +
            "WHERE o.deleted = 0 " +
            "ORDER BY o.created_at DESC " +
            "LIMIT #{limit}")
    List<Order> selectRecentOrders(@Param("limit") Integer limit);
}
