-- 创建通知表
CREATE TABLE IF NOT EXISTS `notification` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '通知ID',
    `title` varchar(255) NOT NULL COMMENT '通知标题',
    `message` text NOT NULL COMMENT '通知内容',
    `type` varchar(50) NOT NULL DEFAULT 'system' COMMENT '通知类型：order-订单，system-系统，warning-警告，success-成功，user-用户',
    `user_id` int DEFAULT NULL COMMENT '接收用户ID，null表示全体用户',
    `is_read` tinyint NOT NULL DEFAULT '0' COMMENT '是否已读：0-未读，1-已读',
    `link` varchar(500) DEFAULT NULL COMMENT '相关链接',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_type` (`type`),
    KEY `idx_is_read` (`is_read`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

-- 插入一些示例通知数据
INSERT INTO `notification` (`title`, `message`, `type`, `user_id`, `is_read`, `link`) VALUES
('系统维护通知', '系统将于今晚22:00-24:00进行维护，期间可能影响正常使用', 'system', NULL, 0, NULL),
('新订单提醒', '您有一个新的订单需要处理，订单号：#001', 'order', NULL, 0, '/orders/1'),
('用户注册通知', '新用户 test353244 已注册成功', 'user', NULL, 0, '/users'),
('菜品库存警告', '宫保鸡丁库存不足，请及时补充', 'warning', NULL, 0, '/dishes'),
('订单完成通知', '订单 #002 已完成制作', 'success', NULL, 1, '/orders/2');
