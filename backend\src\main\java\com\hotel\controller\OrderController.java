package com.hotel.controller;

import com.hotel.common.PageResult;
import com.hotel.common.Result;
import com.hotel.dto.CreateOrderRequest;
import com.hotel.entity.Order;
import com.hotel.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 订单控制器
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Slf4j
@RestController
@RequestMapping("/orders")
public class OrderController {

    @Autowired
    private OrderService orderService;

    /**
     * 分页查询订单列表
     */
    @GetMapping
    public Result<PageResult<Order>> getOrderPage(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String orderId,
            @RequestParam(required = false) String tableNumber,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate) {
        
        PageResult<Order> result = orderService.getOrderPage(page, size, orderId, tableNumber, status, startDate, endDate);
        return Result.success(result);
    }

    /**
     * 根据ID查询订单详情
     */
    @GetMapping("/{id}")
    public Result<Order> getOrderById(@PathVariable Integer id) {
        Order order = orderService.getOrderById(id);
        return Result.success(order);
    }

    /**
     * 创建订单
     */
    @PostMapping
    public Result<Order> createOrder(@Valid @RequestBody CreateOrderRequest request, HttpServletRequest httpRequest) {
        Integer userId = (Integer) httpRequest.getAttribute("userId");
        Order order = orderService.createOrder(request, userId);
        return Result.success("订单创建成功", order);
    }

    /**
     * 更新订单状态
     */
    @PatchMapping("/{id}/status")
    public Result<Order> updateOrderStatus(@PathVariable Integer id, @RequestBody Map<String, String> request) {
        String status = request.get("status");
        Order order = orderService.updateOrderStatus(id, status);
        return Result.success("订单状态更新成功", order);
    }

    /**
     * 删除订单
     */
    @DeleteMapping("/{id}")
    public Result<Object> deleteOrder(@PathVariable Integer id) {
        orderService.deleteOrder(id);
        return Result.success("订单删除成功");
    }

    /**
     * 获取今日统计数据
     */
    @GetMapping("/today-stats")
    public Result<Map<String, Object>> getTodayStats() {
        Map<String, Object> stats = orderService.getTodayStats();
        return Result.success(stats);
    }

    /**
     * 获取订单统计数据
     */
    @GetMapping("/stats")
    public Result<List<Map<String, Object>>> getOrderStats(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate) {
        
        List<Map<String, Object>> stats = orderService.getStatusStats(startDate, endDate);
        return Result.success(stats);
    }

    /**
     * 获取销售趋势数据
     */
    @GetMapping("/sales-trend")
    public Result<List<Map<String, Object>>> getSalesTrend(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate) {
        
        List<Map<String, Object>> trend = orderService.getSalesTrend(startDate, endDate);
        return Result.success(trend);
    }

    /**
     * 获取时段分析数据
     */
    @GetMapping("/hourly-analysis")
    public Result<List<Map<String, Object>>> getHourlyAnalysis() {
        List<Map<String, Object>> analysis = orderService.getHourlyAnalysis();
        return Result.success(analysis);
    }

    /**
     * 获取最近订单
     */
    @GetMapping("/recent")
    public Result<List<Order>> getRecentOrders(@RequestParam(defaultValue = "10") Integer limit) {
        List<Order> orders = orderService.getRecentOrders(limit);
        return Result.success(orders);
    }
}
