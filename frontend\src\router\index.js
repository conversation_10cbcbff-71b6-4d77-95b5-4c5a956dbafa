import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/Register.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: () => import('@/layout/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/SimpleDashboard.vue'),
        meta: { title: '控制面板' }
      },
      {
        path: '/dishes',
        name: 'Dishes',
        component: () => import('@/views/Dishes.vue'),
        meta: { title: '菜品管理' }
      },
      {
        path: '/dishes/add',
        name: 'AddDish',
        component: () => import('@/views/AddDish.vue'),
        meta: { title: '添加菜品' }
      },
      {
        path: '/dishes/edit/:id',
        name: 'EditDish',
        component: () => import('@/views/EditDish.vue'),
        meta: { title: '编辑菜品' }
      },
      {
        path: '/orders',
        name: 'Orders',
        component: () => import('@/views/SimpleOrders.vue'),
        meta: { title: '订单管理' }
      },
      {
        path: '/orders/create',
        name: 'CreateOrder',
        component: () => import('@/views/CreateOrder.vue'),
        meta: { title: '创建订单' }
      },
      {
        path: '/orders/:id',
        name: 'OrderDetail',
        component: () => import('@/views/OrderDetail.vue'),
        meta: { title: '订单详情' }
      },
      {
        path: '/reports',
        name: 'Reports',
        component: () => import('@/views/Reports.vue'),
        meta: { title: '报表统计' }
      },
      {
        path: '/settings',
        name: 'Settings',
        component: () => import('@/views/Settings.vue'),
        meta: { title: '系统设置' }
      },
      {
        path: '/users',
        name: 'Users',
        component: () => import('@/views/Users.vue'),
        meta: { title: '用户管理', requiresAdmin: true }
      },

    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login')
  } else if (to.meta.requiresAdmin && userStore.user?.role !== 'ADMIN') {
    next('/')
  } else if ((to.name === 'Login' || to.name === 'Register') && userStore.isLoggedIn) {
    next('/')
  } else {
    next()
  }
})

export default router
