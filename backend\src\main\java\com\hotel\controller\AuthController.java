package com.hotel.controller;

import com.hotel.common.Result;
import com.hotel.dto.LoginRequest;
import com.hotel.dto.LoginResponse;
import com.hotel.dto.RegisterRequest;
import com.hotel.entity.User;
import com.hotel.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Map;

/**
 * 认证控制器
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private AuthService authService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        LoginResponse response = authService.login(request);
        return Result.success("登录成功", response);
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<User> register(@Valid @RequestBody RegisterRequest request) {
        User user = authService.register(request);
        return Result.success("注册成功", user);
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public Result<User> getCurrentUser(HttpServletRequest request) {
        Integer userId = (Integer) request.getAttribute("userId");
        User user = authService.getCurrentUser(userId);
        return Result.success(user);
    }

    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    public Result<Object> changePassword(@RequestBody Map<String, String> request, HttpServletRequest httpRequest) {
        Integer userId = (Integer) httpRequest.getAttribute("userId");
        String currentPassword = request.get("currentPassword");
        String newPassword = request.get("newPassword");

        authService.changePassword(userId, currentPassword, newPassword);
        return Result.success("密码修改成功");
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Result<Object> logout(HttpServletRequest request) {
        Integer userId = (Integer) request.getAttribute("userId");
        authService.logout(userId);
        return Result.success("退出成功");
    }
}
