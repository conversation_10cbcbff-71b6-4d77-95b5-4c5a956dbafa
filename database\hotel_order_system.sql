-- 酒店点单系统数据库脚本
-- 创建时间: 2023-07-15
-- 版本: 1.0.0

CREATE DATABASE IF NOT EXISTS hotel_order_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE hotel_order_system;

-- 用户表（员工/管理员）
CREATE TABLE `user` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
  `password` VARCHAR(100) NOT NULL COMMENT '密码（加密）',
  `role` ENUM('ADMIN', 'STAFF') NOT NULL DEFAULT 'STAFF' COMMENT '角色：ADMIN-管理员，STAFF-服务员',
  `name` VARCHAR(50) NOT NULL COMMENT '姓名',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `avatar` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
  `last_login_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
  INDEX `idx_username` (`username`),
  INDEX `idx_role` (`role`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 菜品表
CREATE TABLE `dish` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) NOT NULL COMMENT '菜品名称',
  `description` TEXT COMMENT '菜品描述',
  `price` DECIMAL(10,2) NOT NULL COMMENT '价格',
  `category` ENUM('APPETIZER', 'MAIN', 'DESSERT', 'DRINK') NOT NULL COMMENT '分类：APPETIZER-开胃菜，MAIN-主菜，DESSERT-甜点，DRINK-饮品',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-上架，0-下架',
  `image_url` VARCHAR(500) DEFAULT NULL COMMENT '图片URL',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
  INDEX `idx_name` (`name`),
  INDEX `idx_category` (`category`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜品表';

-- 订单表
CREATE TABLE `orders` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `table_number` VARCHAR(20) NOT NULL COMMENT '桌号',
  `user_id` INT NOT NULL COMMENT '服务员ID',
  `status` ENUM('PENDING', 'PREPARING', 'COMPLETED', 'CANCELLED') DEFAULT 'PENDING' COMMENT '状态：PENDING-待处理，PREPARING-准备中，COMPLETED-已完成，CANCELLED-已取消',
  `total_price` DECIMAL(10,2) NOT NULL COMMENT '总价格',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
  FOREIGN KEY (`user_id`) REFERENCES `user`(`id`),
  INDEX `idx_table_number` (`table_number`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 订单明细表
CREATE TABLE `order_item` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `order_id` INT NOT NULL COMMENT '订单ID',
  `dish_id` INT NOT NULL COMMENT '菜品ID',
  `quantity` INT NOT NULL COMMENT '数量',
  `price` DECIMAL(10,2) NOT NULL COMMENT '单价',
  `notes` TEXT COMMENT '备注',
  FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`dish_id`) REFERENCES `dish`(`id`),
  INDEX `idx_order_id` (`order_id`),
  INDEX `idx_dish_id` (`dish_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

-- 插入初始数据

-- 用户数据（密码为BCrypt加密后的123456）
INSERT INTO `user` (username, password, role, name, status) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLO.TAGxK6FO', 'ADMIN', '系统管理员', 1),
('staff1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLO.TAGxK6FO', 'STAFF', '服务员A', 1),
('staff2', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLO.TAGxK6FO', 'STAFF', '服务员B', 1);

-- 菜品数据
INSERT INTO `dish` (name, description, price, category, status, image_url) VALUES
('宫保鸡丁', '经典川菜，麻辣鲜香，配以花生米和青椒，色香味俱全', 38.00, 'MAIN', 1, 'https://images.unsplash.com/photo-1563245372-f21724e3856d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'),
('清蒸鲈鱼', '鲜嫩多汁，清淡可口，保留鱼肉原汁原味', 68.00, 'MAIN', 1, 'https://images.unsplash.com/photo-1606755456206-b25206cde27e?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'),
('水果沙拉', '新鲜时令水果，搭配特制酸奶酱，健康美味', 28.00, 'DESSERT', 1, 'https://images.unsplash.com/photo-1505253716362-afaea1d3d1af?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'),
('可乐', '冰镇可口可乐，畅爽解渴', 10.00, 'DRINK', 1, 'https://images.unsplash.com/photo-1596803244618-8dbee441d70b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'),
('凉拌黄瓜', '爽脆开胃，清香解腻', 18.00, 'APPETIZER', 1, 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'),
('红烧肉', '肥而不腻，入口即化的经典家常菜', 45.00, 'MAIN', 1, 'https://images.unsplash.com/photo-1574484284002-952d92456975?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'),
('蛋炒饭', '粒粒分明，香味浓郁', 25.00, 'MAIN', 1, 'https://images.unsplash.com/photo-1603133872878-684f208fb84b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'),
('绿茶', '清香淡雅，回甘悠长', 15.00, 'DRINK', 1, 'https://images.unsplash.com/photo-1556679343-c7306c1976bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');

-- 示例订单数据
INSERT INTO `orders` (table_number, user_id, status, total_price) VALUES
('A12', 2, 'COMPLETED', 76.00),
('VIP3', 2, 'PREPARING', 106.00),
('B05', 3, 'PENDING', 142.00);

-- 订单明细数据
INSERT INTO `order_item` (order_id, dish_id, quantity, price, notes) VALUES
(1, 1, 2, 38.00, '少辣'),
(2, 2, 1, 68.00, '少盐'),
(2, 3, 1, 28.00, ''),
(2, 4, 1, 10.00, '去冰'),
(3, 1, 1, 38.00, ''),
(3, 5, 2, 18.00, ''),
(3, 6, 1, 45.00, ''),
(3, 7, 1, 25.00, ''),
(3, 8, 1, 15.00, '');

-- 创建索引优化查询性能
CREATE INDEX idx_user_deleted ON `user`(`deleted`);
CREATE INDEX idx_dish_deleted ON `dish`(`deleted`);
CREATE INDEX idx_orders_deleted ON `orders`(`deleted`);

-- 显示表结构
SHOW TABLES;

-- 显示初始数据统计
SELECT '用户数量' as '统计项', COUNT(*) as '数量' FROM `user` WHERE deleted = 0
UNION ALL
SELECT '菜品数量', COUNT(*) FROM `dish` WHERE deleted = 0
UNION ALL
SELECT '订单数量', COUNT(*) FROM `orders` WHERE deleted = 0
UNION ALL
SELECT '订单明细数量', COUNT(*) FROM `order_item`;

-- 完成提示
SELECT '数据库初始化完成！' as '状态', 
       '默认管理员账户: admin/123456' as '登录信息',
       '默认服务员账户: staff1/123456' as '备注';


CREATE TABLE IF NOT EXISTS `notification` (
    ->     `id` int NOT NULL AUTO_INCREMENT COMMENT '通知ID',
    ->     `title` varchar(255) NOT NULL COMMENT '通知标题',
    ->     `message` text NOT NULL COMMENT '通知内容',
    ->     `type` varchar(50) NOT NULL DEFAULT 'system' COMMENT '通知类型：order-订单，system-系统，warning-警告，success-成功，user-用户',
    ->     `user_id` int DEFAULT NULL COMMENT '接收用户ID，null表示全体用户',
    ->     `is_read` tinyint NOT NULL DEFAULT '0' COMMENT '是否已读：0-未读，1-已读',
    ->     `link` varchar(500) DEFAULT NULL COMMENT '相关链接',
    ->     `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    ->     `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    ->     `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除，1-已删除',
    ->     PRIMARY KEY (`id`),
    ->     KEY `idx_user_id` (`user_id`),
    ->     KEY `idx_type` (`type`),
    ->     KEY `idx_is_read` (`is_read`),
    ->     KEY `idx_created_at` (`created_at`),
    ->     KEY `idx_deleted` (`deleted`)
    -> ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

INSERT INTO `notification` (`title`, `message`, `type`, `user_id`, `is_read`, `link`) VALUES
    -> ('系统维护通知', '系统将于今晚22:00-24:00进行维护，期间可能影响正常使用', 'system', NULL, 0, NULL),
    -> ('新订单提醒', '您有一个新的订单需要处理，订单号：#001', 'order', NULL, 0, '/orders/1'),
    -> ('用户注册通知', '新用户 test353244 已注册成功', 'user', NULL, 0, '/users'),
    -> ('菜品库存警告', '宫保鸡丁库存不足，请及时补充', 'warning', NULL, 0, '/dishes'),
    -> ('订单完成通知', '订单 #002 已完成制作', 'success', NULL, 1, '/orders/2');