<template>
  <div class="dashboard">
    <h1>控制面板</h1>

    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #409eff; margin: 0;">42</h2>
          <p style="margin: 5px 0 0 0;">今日订单</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #e6a23c; margin: 0;">8</h2>
          <p style="margin: 5px 0 0 0;">待处理</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #67c23a; margin: 0;">¥2850</h2>
          <p style="margin: 5px 0 0 0;">今日销售</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #f56c6c; margin: 0;">38</h2>
          <p style="margin: 5px 0 0 0;">菜品总数</p>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快速操作 -->
    <el-card>
      <template #header>
        <span>快速操作</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-button type="primary" style="width: 100%;" @click="$router.push('/orders/create')">
            创建订单
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" style="width: 100%;" @click="$router.push('/dishes')">
            管理菜品
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" style="width: 100%;" @click="$router.push('/orders')">
            查看订单
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" style="width: 100%;" @click="$router.push('/reports')">
            查看报表
          </el-button>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()
</script>

<style scoped>
.dashboard {
  padding: 20px;
}
</style>
