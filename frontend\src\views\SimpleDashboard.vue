<template>
  <div class="dashboard">
    <h1 style="color: #409eff; margin-bottom: 20px;">🏨 酒店点单系统 - 控制面板</h1>
    
    <!-- 调试信息 -->
    <el-alert 
      title="✅ 页面加载成功！" 
      type="success" 
      :closable="false"
      style="margin-bottom: 20px;"
    >
      <p>如果您能看到这条消息，说明Dashboard页面已经正常加载</p>
      <p>当前时间: {{ currentTime }}</p>
    </el-alert>
    
    <!-- 简化的统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #409eff; margin: 0;">42</h2>
          <p style="margin: 5px 0 0 0; color: #666;">今日订单</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #e6a23c; margin: 0;">8</h2>
          <p style="margin: 5px 0 0 0; color: #666;">待处理</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #67c23a; margin: 0;">¥2850</h2>
          <p style="margin: 5px 0 0 0; color: #666;">今日销售</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #f56c6c; margin: 0;">38</h2>
          <p style="margin: 5px 0 0 0; color: #666;">菜品总数</p>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快速操作 -->
    <el-card>
      <template #header>
        <span>🚀 快速操作</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-button type="primary" style="width: 100%;" @click="$router.push('/orders/create')">
            📝 创建订单
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" style="width: 100%;" @click="$router.push('/dishes')">
            🍽️ 管理菜品
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" style="width: 100%;" @click="$router.push('/orders')">
            📋 查看订单
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" style="width: 100%;" @click="$router.push('/reports')">
            📊 查看报表
          </el-button>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const currentTime = ref('')

const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

onMounted(() => {
  console.log('SimpleDashboard mounted successfully!')
  updateTime()
  setInterval(updateTime, 1000)
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}
</style>
