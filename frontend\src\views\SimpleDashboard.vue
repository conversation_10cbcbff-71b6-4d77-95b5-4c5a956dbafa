<template>
  <div class="dashboard">
    <h1>控制面板</h1>

    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;" v-loading="loading">
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #409eff; margin: 0;">{{ stats.todayOrders || 0 }}</h2>
          <p style="margin: 5px 0 0 0;">今日订单</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #e6a23c; margin: 0;">{{ stats.pendingOrders || 0 }}</h2>
          <p style="margin: 5px 0 0 0;">待处理</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #67c23a; margin: 0;">¥{{ stats.todaySales || 0 }}</h2>
          <p style="margin: 5px 0 0 0;">今日销售</p>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card style="text-align: center;">
          <h2 style="color: #f56c6c; margin: 0;">{{ stats.totalDishes || 0 }}</h2>
          <p style="margin: 5px 0 0 0;">菜品总数</p>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快速操作 -->
    <el-card>
      <template #header>
        <span>快速操作</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-button type="primary" style="width: 100%;" @click="$router.push('/orders/create')">
            创建订单
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" style="width: 100%;" @click="$router.push('/dishes')">
            管理菜品
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" style="width: 100%;" @click="$router.push('/orders')">
            查看订单
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" style="width: 100%;" @click="$router.push('/reports')">
            查看报表
          </el-button>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getOrderStats } from '@/api/order'
import { getDishes } from '@/api/dish'

const router = useRouter()
const loading = ref(false)
const stats = reactive({
  todayOrders: 0,
  pendingOrders: 0,
  todaySales: 0,
  totalDishes: 0
})

// 加载统计数据
const loadStats = async () => {
  loading.value = true
  try {
    // 获取订单统计
    const orderStatsRes = await getOrderStats()
    if (orderStatsRes.data) {
      Object.assign(stats, orderStatsRes.data)
    }

    // 获取菜品总数
    const dishesRes = await getDishes({ page: 1, size: 1 })
    if (dishesRes.data) {
      stats.totalDishes = dishesRes.data.total || 0
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}
</style>
