import request from './request'

// 获取用户列表
export const getUsers = (params) => {
  return request({
    url: '/api/users',
    method: 'get',
    params
  })
}

// 获取单个用户
export const getUser = (id) => {
  return request({
    url: `/api/users/${id}`,
    method: 'get'
  })
}

// 创建用户
export const createUser = (data) => {
  return request({
    url: '/api/users',
    method: 'post',
    data
  })
}

// 更新用户
export const updateUser = (id, data) => {
  return request({
    url: `/api/users/${id}`,
    method: 'put',
    data
  })
}

// 删除用户
export const deleteUser = (id) => {
  return request({
    url: `/api/users/${id}`,
    method: 'delete'
  })
}

// 重置用户密码
export const resetUserPassword = (id, password) => {
  return request({
    url: `/api/users/${id}/reset-password`,
    method: 'put',
    data: { password }
  })
}

// 切换用户状态
export const toggleUserStatus = (id) => {
  return request({
    url: `/api/users/${id}/toggle-status`,
    method: 'put'
  })
}
