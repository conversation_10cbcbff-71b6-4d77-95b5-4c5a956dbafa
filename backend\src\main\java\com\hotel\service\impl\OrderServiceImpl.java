package com.hotel.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hotel.common.PageResult;
import com.hotel.dto.CreateOrderRequest;
import com.hotel.entity.Order;
import com.hotel.entity.OrderItem;
import com.hotel.exception.BusinessException;
import com.hotel.mapper.OrderItemMapper;
import com.hotel.mapper.OrderMapper;
import com.hotel.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单服务实现类
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderItemMapper orderItemMapper;

    @Override
    public PageResult<Order> getOrderPage(Integer page, Integer size, String orderId, String tableNumber,
                                          String status, LocalDateTime startDate, LocalDateTime endDate) {
        Page<Order> pageParam = new Page<>(page, size);
        var result = orderMapper.selectOrderPage(pageParam, orderId, tableNumber, status, startDate, endDate);
        return PageResult.build(result.getRecords(), result.getTotal(), result.getCurrent(), result.getSize());
    }

    @Override
    public Order getOrderById(Integer id) {
        Order order = orderMapper.selectOrderWithUser(id);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }

        // 查询订单明细
        List<OrderItem> items = orderItemMapper.selectByOrderIdWithDish(id);
        order.setItems(items);

        return order;
    }

    @Override
    @Transactional
    public Order createOrder(CreateOrderRequest request, Integer userId) {
        // 创建订单
        Order order = new Order();
        order.setTableNumber(request.getTableNumber());
        order.setUserId(userId);
        order.setStatus("PENDING");
        order.setTotalPrice(request.getTotalPrice());

        orderMapper.insert(order);

        // 创建订单明细
        List<OrderItem> items = request.getItems().stream().map(itemRequest -> {
            OrderItem item = new OrderItem();
            item.setOrderId(order.getId());
            item.setDishId(itemRequest.getDishId());
            item.setQuantity(itemRequest.getQuantity());
            item.setPrice(itemRequest.getPrice());
            item.setNotes(itemRequest.getNotes());
            return item;
        }).collect(Collectors.toList());

        // 批量插入订单明细
        for (OrderItem item : items) {
            orderItemMapper.insert(item);
        }

        log.info("创建订单成功: 订单ID={}, 桌号={}, 金额={}", order.getId(), order.getTableNumber(), order.getTotalPrice());
        return order;
    }

    @Override
    @Transactional
    public Order updateOrderStatus(Integer id, String status) {
        Order order = orderMapper.selectById(id);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }

        // 验证状态转换
        validateStatusTransition(order.getStatus(), status);

        order.setStatus(status);
        orderMapper.updateById(order);

        log.info("更新订单状态成功: 订单ID={}, 状态={}", id, status);
        return order;
    }

    @Override
    @Transactional
    public void deleteOrder(Integer id) {
        Order order = orderMapper.selectById(id);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }

        // 检查订单状态，准备中的订单不能删除
        if ("PREPARING".equals(order.getStatus())) {
            throw new BusinessException("准备中的订单不能删除");
        }

        // 删除订单明细
        orderItemMapper.deleteByOrderId(id);
        
        // 删除订单
        orderMapper.deleteById(id);

        log.info("删除订单成功: 订单ID={}", id);
    }

    @Override
    public Map<String, Object> getTodayStats() {
        return orderMapper.selectTodayStats();
    }

    @Override
    public List<Map<String, Object>> getStatusStats(LocalDateTime startDate, LocalDateTime endDate) {
        return orderMapper.selectStatusStats(startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> getSalesTrend(LocalDateTime startDate, LocalDateTime endDate) {
        return orderMapper.selectSalesTrend(startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> getHourlyAnalysis() {
        return orderMapper.selectHourlyAnalysis();
    }

    @Override
    public List<Order> getRecentOrders(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        return orderMapper.selectRecentOrders(limit);
    }

    /**
     * 验证状态转换是否合法
     */
    private void validateStatusTransition(String currentStatus, String newStatus) {
        // 已完成和已取消的订单不能再修改状态
        if ("COMPLETED".equals(currentStatus) || "CANCELLED".equals(currentStatus)) {
            throw new BusinessException("订单已完成或已取消，不能修改状态");
        }

        // 其他状态转换规则可以根据业务需求添加
    }
}
