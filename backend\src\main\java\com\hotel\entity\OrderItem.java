package com.hotel.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单明细实体类
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("order_item")
public class OrderItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private Integer orderId;

    /**
     * 菜品ID
     */
    @TableField("dish_id")
    private Integer dishId;

    /**
     * 数量
     */
    @TableField("quantity")
    private Integer quantity;

    /**
     * 单价
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 备注
     */
    @TableField("notes")
    private String notes;

    /**
     * 菜品名称（非数据库字段）
     */
    @TableField(exist = false)
    private String dishName;

    /**
     * 菜品图片（非数据库字段）
     */
    @TableField(exist = false)
    private String dishImage;
}
