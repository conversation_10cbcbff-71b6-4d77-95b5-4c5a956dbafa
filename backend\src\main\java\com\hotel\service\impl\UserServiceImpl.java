package com.hotel.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hotel.common.PageResult;
import com.hotel.entity.User;
import com.hotel.exception.BusinessException;
import com.hotel.mapper.UserMapper;
import com.hotel.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public PageResult<User> getUserPage(Integer page, Integer size, String username, String name, String role) {
        Page<User> pageParam = new Page<>(page, size);
        var result = userMapper.selectUserPage(pageParam, username, name, role);
        return PageResult.build(result.getRecords(), result.getTotal(), result.getCurrent(), result.getSize());
    }

    @Override
    public User getUserById(Integer id) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return user;
    }

    @Override
    @Transactional
    public User createUser(User user) {
        // 检查用户名是否已存在
        if (userMapper.checkUsernameExists(user.getUsername()) > 0) {
            throw new BusinessException("用户名已存在");
        }

        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // 设置默认状态
        if (user.getStatus() == null) {
            user.setStatus(1);
        }

        userMapper.insert(user);
        log.info("创建用户成功: {}", user.getUsername());
        return user;
    }

    @Override
    @Transactional
    public User updateUser(Integer id, User user) {
        User existingUser = getUserById(id);

        // 检查用户名是否重复（排除自己）
        if (!existingUser.getUsername().equals(user.getUsername()) &&
            userMapper.checkUsernameExists(user.getUsername()) > 0) {
            throw new BusinessException("用户名已存在");
        }

        // 更新字段
        existingUser.setName(user.getName());
        existingUser.setRole(user.getRole());
        
        if (user.getAvatar() != null) {
            existingUser.setAvatar(user.getAvatar());
        }

        userMapper.updateById(existingUser);
        log.info("更新用户成功: {}", existingUser.getUsername());
        return existingUser;
    }

    @Override
    @Transactional
    public void deleteUser(Integer id) {
        User user = getUserById(id);
        
        // 检查是否是最后一个管理员
        if ("ADMIN".equals(user.getRole()) && userMapper.countAdmins() <= 1) {
            throw new BusinessException("不能删除最后一个管理员");
        }

        userMapper.deleteById(id);
        log.info("删除用户成功: {}", user.getUsername());
    }

    @Override
    @Transactional
    public void resetUserPassword(Integer id, String newPassword) {
        User user = getUserById(id);
        user.setPassword(passwordEncoder.encode(newPassword));
        userMapper.updateById(user);
        log.info("重置用户密码成功: {}", user.getUsername());
    }

    @Override
    @Transactional
    public User toggleUserStatus(Integer id) {
        User user = getUserById(id);
        
        // 检查是否是最后一个管理员
        if ("ADMIN".equals(user.getRole()) && user.getStatus() == 1 && userMapper.countAdmins() <= 1) {
            throw new BusinessException("不能禁用最后一个管理员");
        }

        user.setStatus(user.getStatus() == 1 ? 0 : 1);
        userMapper.updateById(user);
        
        String action = user.getStatus() == 1 ? "启用" : "禁用";
        log.info("{}用户成功: {}", action, user.getUsername());
        return user;
    }
}
