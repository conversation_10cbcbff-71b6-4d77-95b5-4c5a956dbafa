<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>酒店点单系统 - 连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #666;
            margin-top: 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.online {
            background: #28a745;
            color: white;
        }
        .status.offline {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏨 酒店点单系统 - 连接测试</h1>
        
        <div class="test-section">
            <h3>📡 服务状态检查</h3>
            <p>前端服务: <span id="frontend-status" class="status offline">检查中...</span></p>
            <p>后端服务: <span id="backend-status" class="status offline">检查中...</span></p>
            <button onclick="checkServices()">重新检查</button>
        </div>

        <div class="test-section">
            <h3>🔌 API 连接测试</h3>
            <button onclick="testDishesAPI()">测试菜品API</button>
            <button onclick="testLoginAPI()">测试登录API</button>
            <button onclick="testCORS()">测试跨域</button>
            <div id="api-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 系统信息</h3>
            <div id="system-info" class="result info">
前端地址: http://localhost:3000
后端地址: http://localhost:8080
代理配置: /api -> http://localhost:8080
            </div>
        </div>
    </div>

    <script>
        // 检查服务状态
        async function checkServices() {
            // 检查前端
            const frontendStatus = document.getElementById('frontend-status');
            frontendStatus.textContent = '在线';
            frontendStatus.className = 'status online';

            // 检查后端
            const backendStatus = document.getElementById('backend-status');
            try {
                const response = await fetch('http://localhost:8080/api/dishes');
                if (response.ok) {
                    backendStatus.textContent = '在线';
                    backendStatus.className = 'status online';
                } else {
                    backendStatus.textContent = '离线';
                    backendStatus.className = 'status offline';
                }
            } catch (error) {
                backendStatus.textContent = '离线';
                backendStatus.className = 'status offline';
            }
        }

        // 测试菜品API
        async function testDishesAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试菜品API...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch('http://localhost:8080/api/dishes');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 菜品API测试成功！
状态码: ${response.status}
数据: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 菜品API测试失败！
状态码: ${response.status}
错误: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 菜品API测试失败！
错误: ${error.message}`;
            }
        }

        // 测试登录API
        async function testLoginAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试登录API...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'test',
                        password: 'test'
                    })
                });
                const data = await response.json();
                
                resultDiv.className = 'result info';
                resultDiv.textContent = `ℹ️ 登录API响应（预期失败）:
状态码: ${response.status}
响应: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 登录API测试失败！
错误: ${error.message}`;
            }
        }

        // 测试跨域
        async function testCORS() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试跨域配置...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch('http://localhost:8080/api/dishes', {
                    method: 'OPTIONS'
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };

                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 跨域配置测试完成！
状态码: ${response.status}
CORS头部: ${JSON.stringify(corsHeaders, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 跨域测试失败！
错误: ${error.message}`;
            }
        }

        // 页面加载时自动检查服务状态
        window.onload = function() {
            checkServices();
        };
    </script>
</body>
</html>
