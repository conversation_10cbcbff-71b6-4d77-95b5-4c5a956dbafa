package com.hotel.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hotel.common.PageResult;
import com.hotel.entity.Dish;
import com.hotel.exception.BusinessException;
import com.hotel.mapper.DishMapper;
import com.hotel.service.DishService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 菜品服务实现类
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Slf4j
@Service
public class DishServiceImpl implements DishService {

    @Autowired
    private DishMapper dishMapper;

    @Value("${file.upload.path}")
    private String uploadPath;

    @Value("${file.upload.static-path}")
    private String staticPath;

    @Override
    public PageResult<Dish> getDishPage(Integer page, Integer size, String name, String category, Integer status) {
        Page<Dish> pageParam = new Page<>(page, size);
        var result = dishMapper.selectDishPage(pageParam, name, category, status);
        return PageResult.build(result.getRecords(), result.getTotal(), result.getCurrent(), result.getSize());
    }

    @Override
    public Dish getDishById(Integer id) {
        Dish dish = dishMapper.selectById(id);
        if (dish == null) {
            throw new BusinessException("菜品不存在");
        }
        return dish;
    }

    @Override
    @Transactional
    public Dish createDish(Dish dish) {
        // 检查菜品名称是否重复
        if (dishMapper.checkDishNameExists(dish.getName(), 0) > 0) {
            throw new BusinessException("菜品名称已存在");
        }

        // 设置默认状态
        if (dish.getStatus() == null) {
            dish.setStatus(1);
        }

        dishMapper.insert(dish);
        log.info("创建菜品成功: {}", dish.getName());
        return dish;
    }

    @Override
    @Transactional
    public Dish updateDish(Integer id, Dish dish) {
        Dish existingDish = getDishById(id);

        // 检查菜品名称是否重复（排除自己）
        if (!existingDish.getName().equals(dish.getName()) &&
            dishMapper.checkDishNameExists(dish.getName(), id) > 0) {
            throw new BusinessException("菜品名称已存在");
        }

        // 更新字段
        existingDish.setName(dish.getName());
        existingDish.setDescription(dish.getDescription());
        existingDish.setPrice(dish.getPrice());
        existingDish.setCategory(dish.getCategory());
        existingDish.setStatus(dish.getStatus());
        
        if (StringUtils.hasText(dish.getImageUrl())) {
            existingDish.setImageUrl(dish.getImageUrl());
        }

        dishMapper.updateById(existingDish);
        log.info("更新菜品成功: {}", existingDish.getName());
        return existingDish;
    }

    @Override
    @Transactional
    public void deleteDish(Integer id) {
        Dish dish = getDishById(id);
        dishMapper.deleteById(id);
        log.info("删除菜品成功: {}", dish.getName());
    }

    @Override
    public List<Dish> getAvailableDishes() {
        return dishMapper.selectAvailableDishes();
    }

    @Override
    public List<Dish> getDishesByCategory(String category) {
        return dishMapper.selectDishesByCategory(category);
    }

    @Override
    public List<Map<String, Object>> getCategoryStats() {
        return dishMapper.selectCategoryStats();
    }

    @Override
    public List<Map<String, Object>> getHotDishes(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        return dishMapper.selectHotDishes(limit);
    }

    @Override
    public String uploadDishImage(byte[] imageData, String originalFilename) {
        try {
            // 生成文件名
            String extension = getFileExtension(originalFilename);
            String filename = UUID.randomUUID().toString() + "." + extension;
            
            // 创建上传目录
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }
            
            // 保存文件
            File file = new File(uploadDir, filename);
            try (FileOutputStream fos = new FileOutputStream(file)) {
                fos.write(imageData);
            }
            
            // 返回访问URL
            String imageUrl = staticPath + filename;
            log.info("图片上传成功: {}", imageUrl);
            return imageUrl;
            
        } catch (IOException e) {
            log.error("图片上传失败", e);
            throw new BusinessException("图片上传失败");
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "jpg";
        }
        return filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
    }
}
