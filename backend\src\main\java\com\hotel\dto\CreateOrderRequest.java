package com.hotel.dto;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;

/**
 * 创建订单请求DTO
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Data
public class CreateOrderRequest {

    /**
     * 桌号
     */
    @NotBlank(message = "桌号不能为空")
    private String tableNumber;

    /**
     * 总价格
     */
    @NotNull(message = "总价格不能为空")
    @Positive(message = "总价格必须大于0")
    private BigDecimal totalPrice;

    /**
     * 订单明细列表
     */
    @NotEmpty(message = "订单明细不能为空")
    @Valid
    private List<OrderItemRequest> items;

    /**
     * 订单明细请求
     */
    @Data
    public static class OrderItemRequest {

        /**
         * 菜品ID
         */
        @NotNull(message = "菜品ID不能为空")
        private Integer dishId;

        /**
         * 菜品名称
         */
        @NotBlank(message = "菜品名称不能为空")
        private String dishName;

        /**
         * 数量
         */
        @NotNull(message = "数量不能为空")
        @Positive(message = "数量必须大于0")
        private Integer quantity;

        /**
         * 单价
         */
        @NotNull(message = "单价不能为空")
        @Positive(message = "单价必须大于0")
        private BigDecimal price;

        /**
         * 备注
         */
        private String notes;
    }
}
