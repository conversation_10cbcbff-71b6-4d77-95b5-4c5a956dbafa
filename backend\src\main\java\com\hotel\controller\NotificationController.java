package com.hotel.controller;

import com.hotel.common.Result;
import com.hotel.common.PageResult;
import com.hotel.entity.Notification;
import com.hotel.service.NotificationService;
import com.hotel.utils.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 通知控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/notifications")
public class NotificationController {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private JwtUtils jwtUtils;
    
    /**
     * 获取用户通知列表
     */
    @GetMapping
    public Result<PageResult<Notification>> getNotifications(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            HttpServletRequest request) {
        
        Integer userId = getUserIdFromRequest(request);
        PageResult<Notification> result = notificationService.getUserNotifications(userId, page, size);
        return Result.success(result);
    }
    
    /**
     * 获取未读通知数量
     */
    @GetMapping("/unread-count")
    public Result<Integer> getUnreadCount(HttpServletRequest request) {
        Integer userId = getUserIdFromRequest(request);
        Integer count = notificationService.getUnreadCount(userId);
        return Result.success(count);
    }

    /**
     * 标记通知为已读
     */
    @PutMapping("/{id}/read")
    public Result<Void> markAsRead(@PathVariable Integer id, HttpServletRequest request) {
        Integer userId = getUserIdFromRequest(request);
        notificationService.markAsRead(id, userId);
        return Result.success();
    }

    /**
     * 标记所有通知为已读
     */
    @PutMapping("/read-all")
    public Result<Void> markAllAsRead(HttpServletRequest request) {
        Integer userId = getUserIdFromRequest(request);
        notificationService.markAllAsRead(userId);
        return Result.success();
    }

    /**
     * 删除通知
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteNotification(@PathVariable Integer id, HttpServletRequest request) {
        Integer userId = getUserIdFromRequest(request);
        // 这里可以添加权限检查，确保用户只能删除自己的通知
        notificationService.removeById(id);
        return Result.success();
    }

    /**
     * 从请求中获取用户ID
     */
    private Integer getUserIdFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            String token = bearerToken.substring(7);
            return jwtUtils.getUserIdFromToken(token);
        }
        throw new RuntimeException("未找到有效的Token");
    }
}
