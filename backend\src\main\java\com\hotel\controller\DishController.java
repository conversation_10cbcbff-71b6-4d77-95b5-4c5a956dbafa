package com.hotel.controller;

import com.hotel.common.PageResult;
import com.hotel.common.Result;
import com.hotel.entity.Dish;
import com.hotel.service.DishService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 菜品控制器
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Slf4j
@RestController
@RequestMapping("/dishes")
public class DishController {

    @Autowired
    private DishService dishService;

    /**
     * 分页查询菜品列表
     */
    @GetMapping
    public Result<PageResult<Dish>> getDishPage(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "12") Integer size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) Integer status) {
        
        PageResult<Dish> result = dishService.getDishPage(page, size, name, category, status);
        return Result.success(result);
    }

    /**
     * 根据ID查询菜品
     */
    @GetMapping("/api/{id}")
    public Result<Dish> getDishById(@PathVariable Integer id) {
        Dish dish = dishService.getDishById(id);
        return Result.success(dish);
    }

    /**
     * 创建菜品
     */
    @PostMapping
    public Result<Dish> createDish(@Valid @RequestBody Dish dish) {
        Dish result = dishService.createDish(dish);
        return Result.success("菜品创建成功", result);
    }

    /**
     * 更新菜品
     */
    @PutMapping("/{id}")
    public Result<Dish> updateDish(@PathVariable Integer id, @Valid @RequestBody Dish dish) {
        Dish result = dishService.updateDish(id, dish);
        return Result.success("菜品更新成功", result);
    }

    /**
     * 删除菜品
     */
    @DeleteMapping("/{id}")
    public Result<Object> deleteDish(@PathVariable Integer id) {
        dishService.deleteDish(id);
        return Result.success("菜品删除成功");
    }

    /**
     * 获取可用菜品列表
     */
    @GetMapping("/available")
    public Result<List<Dish>> getAvailableDishes() {
        List<Dish> dishes = dishService.getAvailableDishes();
        return Result.success(dishes);
    }

    /**
     * 根据分类获取菜品
     */
    @GetMapping("/category/{category}")
    public Result<List<Dish>> getDishesByCategory(@PathVariable String category) {
        List<Dish> dishes = dishService.getDishesByCategory(category);
        return Result.success(dishes);
    }

    /**
     * 获取菜品分类统计
     */
    @GetMapping("/category-stats")
    public Result<List<Map<String, Object>>> getCategoryStats() {
        List<Map<String, Object>> stats = dishService.getCategoryStats();
        return Result.success(stats);
    }

    /**
     * 获取热销菜品
     */
    @GetMapping("/hot")
    public Result<List<Map<String, Object>>> getHotDishes(
            @RequestParam(defaultValue = "10") Integer limit) {
        List<Map<String, Object>> dishes = dishService.getHotDishes(limit);
        return Result.success(dishes);
    }

    /**
     * 上传菜品图片
     */
    @PostMapping("/upload")
    public Result<Map<String, String>> uploadImage(@RequestParam("file") MultipartFile file) {
        try {
            String imageUrl = dishService.uploadDishImage(file.getBytes(), file.getOriginalFilename());
            return Result.success("图片上传成功", Map.of("url", imageUrl));
        } catch (Exception e) {
            log.error("图片上传失败", e);
            return Result.error("图片上传失败");
        }
    }
}
