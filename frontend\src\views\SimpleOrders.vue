<template>
  <div class="orders-page">
    <h1>订单管理</h1>
    
    <!-- 搜索筛选 -->
    <el-card style="margin-bottom: 20px;">
      <el-form :model="searchForm" inline>
        <el-form-item label="桌号">
          <el-input v-model="searchForm.tableNumber" placeholder="请输入桌号" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="待处理" value="PENDING" />
            <el-option label="准备中" value="PREPARING" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 订单列表 -->
    <el-card v-loading="loading">
      <el-table :data="orders" style="width: 100%">
        <el-table-column prop="id" label="订单号" width="100">
          <template #default="{ row }">
            #{{ row.id }}
          </template>
        </el-table-column>
        <el-table-column prop="tableNumber" label="桌号" width="100" />
        <el-table-column prop="userName" label="服务员" width="120" />
        <el-table-column prop="totalPrice" label="金额" width="100">
          <template #default="{ row }">
            ¥{{ row.totalPrice }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="text" @click="viewOrder(row.id)">查看</el-button>
            <el-button 
              v-if="row.status === 'PENDING'" 
              type="text" 
              @click="updateStatus(row.id, 'PREPARING')"
            >
              开始准备
            </el-button>
            <el-button 
              v-if="row.status === 'PREPARING'" 
              type="text" 
              @click="updateStatus(row.id, 'COMPLETED')"
            >
              完成
            </el-button>
            <el-button 
              v-if="['PENDING', 'PREPARING'].includes(row.status)" 
              type="text" 
              class="danger"
              @click="updateStatus(row.id, 'CANCELLED')"
            >
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: center;">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getOrders, updateOrderStatus } from '@/api/order'

const router = useRouter()
const loading = ref(false)
const orders = ref([])

const searchForm = reactive({
  tableNumber: '',
  status: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 获取订单列表
const fetchOrders = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const response = await getOrders(params)
    orders.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchOrders()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    tableNumber: '',
    status: ''
  })
  handleSearch()
}

// 分页
const handleSizeChange = (size) => {
  pagination.size = size
  fetchOrders()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchOrders()
}

// 状态相关
const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'PREPARING': 'info',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待处理',
    'PREPARING': '准备中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 查看订单详情
const viewOrder = (orderId) => {
  router.push(`/orders/${orderId}`)
}

// 更新订单状态
const updateStatus = async (orderId, status) => {
  try {
    const statusText = getStatusText(status)
    await ElMessageBox.confirm(`确定要将订单状态更改为"${statusText}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await updateOrderStatus(orderId, status)
    ElMessage.success('状态更新成功')
    fetchOrders()
  } catch (error) {
    if (error.message) {
      ElMessage.error('状态更新失败')
    }
  }
}

onMounted(() => {
  fetchOrders()
})
</script>

<style scoped>
.orders-page {
  padding: 20px;
}

.danger {
  color: #f56c6c;
}
</style>
