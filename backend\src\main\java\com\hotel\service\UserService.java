package com.hotel.service;

import com.hotel.common.PageResult;
import com.hotel.entity.User;

/**
 * 用户服务接口
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
public interface UserService {

    /**
     * 分页查询用户列表
     */
    PageResult<User> getUserPage(Integer page, Integer size, String username, String name, String role);

    /**
     * 根据ID查询用户
     */
    User getUserById(Integer id);

    /**
     * 创建用户
     */
    User createUser(User user);

    /**
     * 更新用户
     */
    User updateUser(Integer id, User user);

    /**
     * 删除用户
     */
    void deleteUser(Integer id);

    /**
     * 重置用户密码
     */
    void resetUserPassword(Integer id, String newPassword);

    /**
     * 切换用户状态
     */
    User toggleUserStatus(Integer id);
}
