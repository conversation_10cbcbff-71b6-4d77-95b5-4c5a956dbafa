package com.hotel.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hotel.common.PageResult;
import com.hotel.entity.Dish;

import java.util.List;
import java.util.Map;

/**
 * 菜品服务接口
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
public interface DishService {

    /**
     * 分页查询菜品列表
     */
    PageResult<Dish> getDishPage(Integer page, Integer size, String name, String category, Integer status);

    /**
     * 根据ID查询菜品
     */
    Dish getDishById(Integer id);

    /**
     * 创建菜品
     */
    Dish createDish(Dish dish);

    /**
     * 更新菜品
     */
    Dish updateDish(Integer id, Dish dish);

    /**
     * 删除菜品
     */
    void deleteDish(Integer id);

    /**
     * 获取可用菜品列表
     */
    List<Dish> getAvailableDishes();

    /**
     * 根据分类获取菜品
     */
    List<Dish> getDishesByCategory(String category);

    /**
     * 获取菜品分类统计
     */
    List<Map<String, Object>> getCategoryStats();

    /**
     * 获取热销菜品
     */
    List<Map<String, Object>> getHotDishes(Integer limit);

    /**
     * 上传菜品图片
     */
    String uploadDishImage(byte[] imageData, String originalFilename);
}
