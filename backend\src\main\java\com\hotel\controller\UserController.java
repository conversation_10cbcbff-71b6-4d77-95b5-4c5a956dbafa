package com.hotel.controller;

import com.hotel.common.PageResult;
import com.hotel.common.Result;
import com.hotel.dto.UserCreateRequest;
import com.hotel.entity.User;
import com.hotel.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Map;

/**
 * 用户控制器
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Slf4j
@RestController
@RequestMapping("/users")
@PreAuthorize("hasRole('ADMIN')")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 分页查询用户列表
     */
    @GetMapping
    public Result<PageResult<User>> getUserPage(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String role) {
        
        PageResult<User> result = userService.getUserPage(page, size, username, name, role);
        return Result.success(result);
    }

    /**
     * 根据ID查询用户
     */
    @GetMapping("/{id}")
    public Result<User> getUserById(@PathVariable Integer id) {
        User user = userService.getUserById(id);
        return Result.success(user);
    }

    /**
     * 创建用户
     */
    @PostMapping
    public Result<User> createUser(@Valid @RequestBody UserCreateRequest request) {
        // 创建User对象
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(request.getPassword());
        user.setName(request.getName());
        user.setRole(request.getRole());

        User result = userService.createUser(user);
        return Result.success("用户创建成功", result);
    }

    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    public Result<User> updateUser(@PathVariable Integer id, @Valid @RequestBody User user) {
        User result = userService.updateUser(id, user);
        return Result.success("用户更新成功", result);
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public Result<Object> deleteUser(@PathVariable Integer id) {
        userService.deleteUser(id);
        return Result.success("用户删除成功");
    }

    /**
     * 重置用户密码
     */
    @PostMapping("/{id}/reset-password")
    public Result<Object> resetUserPassword(@PathVariable Integer id, @RequestBody Map<String, String> request) {
        String newPassword = request.get("password");
        userService.resetUserPassword(id, newPassword);
        return Result.success("密码重置成功");
    }

    /**
     * 切换用户状态
     */
    @PostMapping("/{id}/toggle-status")
    public Result<User> toggleUserStatus(@PathVariable Integer id) {
        User user = userService.toggleUserStatus(id);
        return Result.success("用户状态更新成功", user);
    }
}
