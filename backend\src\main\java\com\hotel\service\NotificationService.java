package com.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hotel.entity.Notification;
import com.hotel.common.PageResult;

/**
 * 通知服务接口
 */
public interface NotificationService extends IService<Notification> {
    
    /**
     * 获取用户通知列表
     */
    PageResult<Notification> getUserNotifications(Integer userId, Integer page, Integer size);
    
    /**
     * 标记通知为已读
     */
    void markAsRead(Integer id, Integer userId);
    
    /**
     * 标记所有通知为已读
     */
    void markAllAsRead(Integer userId);
    
    /**
     * 获取未读通知数量
     */
    Integer getUnreadCount(Integer userId);
    
    /**
     * 创建系统通知
     */
    void createSystemNotification(String title, String message, String type);
    
    /**
     * 创建用户通知
     */
    void createUserNotification(Integer userId, String title, String message, String type, String link);
    
    /**
     * 创建订单通知
     */
    void createOrderNotification(Integer orderId, String title, String message);

    /**
     * 删除通知
     */
    void removeById(Integer id);
}
