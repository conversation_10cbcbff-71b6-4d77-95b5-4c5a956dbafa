import request from './request'

// 获取订单列表
export const getOrders = (params) => {
  return request({
    url: '/api/orders',
    method: 'get',
    params
  })
}

// 获取单个订单
export const getOrder = (id) => {
  return request({
    url: `/api/orders/${id}`,
    method: 'get'
  })
}

// 创建订单
export const createOrder = (data) => {
  return request({
    url: '/api/orders',
    method: 'post',
    data
  })
}

// 更新订单状态
export const updateOrderStatus = (id, status) => {
  return request({
    url: `/api/orders/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 取消订单
export const cancelOrder = (id) => {
  return request({
    url: `/api/orders/${id}/cancel`,
    method: 'put'
  })
}

// 获取订单统计
export const getOrderStats = () => {
  return request({
    url: '/api/orders/stats',
    method: 'get'
  })
}

// 获取销售趋势
export const getSalesTrend = (startDate, endDate) => {
  return request({
    url: '/api/orders/sales-trend',
    method: 'get',
    params: { startDate, endDate }
  })
}

// 获取时段分析
export const getHourlyAnalysis = () => {
  return request({
    url: '/api/orders/hourly-analysis',
    method: 'get'
  })
}
