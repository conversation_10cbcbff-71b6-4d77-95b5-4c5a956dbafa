package com.hotel.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 菜品实体类
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("dish")
public class Dish implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 菜品名称
     */
    @TableField("name")
    private String name;

    /**
     * 菜品描述
     */
    @TableField("description")
    private String description;

    /**
     * 价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 分类：APPETIZER-开胃菜，MAIN-主菜，DESSERT-甜点，DRINK-饮品
     */
    @TableField("category")
    private String category;

    /**
     * 状态：1-上架，0-下架
     */
    @TableField("status")
    private Integer status;

    /**
     * 图片URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标志
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;
}
