package com.hotel.service;

import com.hotel.common.PageResult;
import com.hotel.dto.CreateOrderRequest;
import com.hotel.entity.Order;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 订单服务接口
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
public interface OrderService {

    /**
     * 分页查询订单列表
     */
    PageResult<Order> getOrderPage(Integer page, Integer size, String orderId, String tableNumber, 
                                   String status, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 根据ID查询订单详情
     */
    Order getOrderById(Integer id);

    /**
     * 创建订单
     */
    Order createOrder(CreateOrderRequest request, Integer userId);

    /**
     * 更新订单状态
     */
    Order updateOrderStatus(Integer id, String status);

    /**
     * 删除订单
     */
    void deleteOrder(Integer id);

    /**
     * 获取今日统计数据
     */
    Map<String, Object> getTodayStats();

    /**
     * 获取订单状态统计
     */
    List<Map<String, Object>> getStatusStats(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 获取销售趋势数据
     */
    List<Map<String, Object>> getSalesTrend(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 获取时段分析数据
     */
    List<Map<String, Object>> getHourlyAnalysis();

    /**
     * 获取最近订单
     */
    List<Order> getRecentOrders(Integer limit);
}
