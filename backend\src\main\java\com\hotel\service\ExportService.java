package com.hotel.service;

import java.io.ByteArrayOutputStream;

/**
 * 导出服务接口
 */
public interface ExportService {
    
    /**
     * 导出订单报表
     */
    ByteArrayOutputStream exportOrders(String startDate, String endDate);
    
    /**
     * 导出用户报表
     */
    ByteArrayOutputStream exportUsers();
    
    /**
     * 导出菜品报表
     */
    ByteArrayOutputStream exportDishes();
    
    /**
     * 导出销售报表
     */
    ByteArrayOutputStream exportSales(String startDate, String endDate);
}
