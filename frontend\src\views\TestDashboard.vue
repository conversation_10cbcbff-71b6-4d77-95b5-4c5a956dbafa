<template>
  <div class="test-dashboard">
    <h1>测试Dashboard</h1>
    
    <!-- 用户信息测试 -->
    <el-card class="user-info-card">
      <template #header>
        <span>用户信息</span>
      </template>
      <div v-if="userStore.user">
        <p><strong>用户名:</strong> {{ userStore.user.username }}</p>
        <p><strong>姓名:</strong> {{ userStore.user.name }}</p>
        <p><strong>角色:</strong> {{ userStore.user.role }}</p>
        <p><strong>登录状态:</strong> {{ userStore.isLoggedIn ? '已登录' : '未登录' }}</p>
      </div>
      <div v-else>
        <p>用户信息未加载</p>
      </div>
    </el-card>

    <!-- 简单统计卡片 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">42</div>
            <div class="stat-label">今日订单</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">8</div>
            <div class="stat-label">待处理</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">¥2850</div>
            <div class="stat-label">今日销售</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">38</div>
            <div class="stat-label">菜品总数</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 测试按钮 -->
    <div style="margin-top: 20px;">
      <el-button type="primary" @click="testAPI">测试API连接</el-button>
      <el-button type="success" @click="refreshData">刷新数据</el-button>
    </div>

    <!-- API测试结果 -->
    <el-card v-if="apiResult" style="margin-top: 20px;">
      <template #header>
        <span>API测试结果</span>
      </template>
      <pre>{{ apiResult }}</pre>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import request from '@/api/request'

const userStore = useUserStore()
const apiResult = ref(null)

const testAPI = async () => {
  try {
    const response = await request.get('/api/dishes')
    apiResult.value = JSON.stringify(response.data, null, 2)
    ElMessage.success('API连接成功')
  } catch (error) {
    apiResult.value = `API错误: ${error.message}`
    ElMessage.error('API连接失败')
  }
}

const refreshData = () => {
  userStore.initUser()
  ElMessage.info('数据已刷新')
}

onMounted(() => {
  console.log('TestDashboard mounted')
  console.log('User store:', userStore)
  console.log('User:', userStore.user)
  console.log('Is logged in:', userStore.isLoggedIn)
})
</script>

<style scoped>
.test-dashboard {
  padding: 20px;
}

.user-info-card {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
